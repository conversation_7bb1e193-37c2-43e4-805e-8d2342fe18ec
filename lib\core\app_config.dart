class AppConfig {
  static const String appName = '<PERSON><PERSON> thống đặt xe nội bộ';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:3000/api';
  static const String socketUrl = 'ws://localhost:3000';
  
  // Google Maps API Key
  static const String googleMapsApiKey = 'AIzaSyBVVXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'; // Replace with your actual API key
  
  // App Settings
  static const int autoRefreshInterval = 5; // seconds
  static const double maxDistanceKm = 50.0; // km
  static const int maxBookingHours = 24; // hours
  
  // Commission & Penalty Settings
  static const double defaultCommissionRate = 0.1; // 10%
  static const double cancelPenaltyAmount = 50000; // VND
  static const double noShowPenaltyAmount = 100000; // VND
  
  // Vehicle Types
  static const List<String> vehicleTypes = [
    '4 chỗ',
    '7 chỗ', 
    '9 chỗ',
    '16 chỗ'
  ];
  
  // Booking Status
  static const String statusPending = 'pending';
  static const String statusAccepted = 'accepted';
  static const String statusInProgress = 'in_progress';
  static const String statusCompleted = 'completed';
  static const String statusCancelled = 'cancelled';
  
  // User Roles
  static const String roleAdmin = 'admin';
  static const String roleDriver = 'driver';
}
