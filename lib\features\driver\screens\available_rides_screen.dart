import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../models/booking_model.dart';
import '../../../services/real_time_service.dart';
import '../../../services/mock_data_service.dart';

class AvailableRidesScreen extends StatefulWidget {
  @override
  _AvailableRidesScreenState createState() => _AvailableRidesScreenState();
}

class _AvailableRidesScreenState extends State<AvailableRidesScreen> {
  final RealTimeService _realTimeService = RealTimeService();
  bool _isAutoUpdateEnabled = true;

  @override
  void initState() {
    super.initState();
    _realTimeService.startRidesAutoUpdate();
  }

  @override
  void dispose() {
    _realTimeService.stopRidesAutoUpdate();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.directions_car, color: Colors.blue),
            SizedBox(width: 8),
            Text(
              'Hệ Thống Đặt Xe',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          Container(
            margin: EdgeInsets.only(right: 16),
            child: Row(
              children: [
                StreamBuilder<List<dynamic>>(
                  stream: _realTimeService.ridesStream,
                  builder: (context, snapshot) {
                    return Row(
                      children: [
                        Icon(
                          Icons.wifi,
                          color: snapshot.hasData ? Colors.green : Colors.grey,
                          size: 16,
                        ),
                        SizedBox(width: 4),
                        Text(
                          snapshot.hasData ? 'Cập nhật sau 5s' : 'Đang tải...',
                          style: TextStyle(
                            color: snapshot.hasData ? Colors.green : Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Status Bar
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade400, Colors.blue.shade600],
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Trạng thái',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                    Text(
                      'Đang hoạt động',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Vị trí hiện tại',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                    Text(
                      'Quận 1, TP.HCM',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Tab Navigation
          Container(
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(color: Colors.blue, width: 3),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.list_alt, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'Cuốc Xe\nKhả Dụng',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.pushNamed(context, '/driver/create-ride'),
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade300, width: 2),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.add_circle, color: Colors.grey.shade600),
                          SizedBox(width: 8),
                          Text(
                            'Tạo Cuốc',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.pushNamed(context, '/driver/ride-history'),
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade300, width: 2),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.history, color: Colors.grey.shade600),
                          SizedBox(width: 8),
                          Text(
                            'Lịch Sử',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Header with GPS tracking
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Cuốc Xe Gần Bạn',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                Row(
                  children: [
                    Icon(Icons.gps_fixed, color: Colors.blue, size: 16),
                    SizedBox(width: 4),
                    Text(
                      'Theo vị trí GPS',
                      style: TextStyle(
                        color: Colors.blue,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Rides List with Real-time Updates
          Expanded(
            child: StreamBuilder<List<dynamic>>(
              stream: _realTimeService.ridesStream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Đang tải cuốc xe...'),
                      ],
                    ),
                  );
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error, color: Colors.red, size: 48),
                        SizedBox(height: 16),
                        Text('Có lỗi xảy ra khi tải dữ liệu'),
                        SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () => _realTimeService.refreshRides(),
                          child: Text('Thử lại'),
                        ),
                      ],
                    ),
                  );
                }

                final rides = snapshot.data ?? [];
                final availableRides = rides.where((ride) =>
                  ride['status'] == 'pending' &&
                  ride['departureTime']?.isAfter(DateTime.now()) == true
                ).toList();

                if (availableRides.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.directions_car_outlined, 
                             color: Colors.grey, size: 64),
                        SizedBox(height: 16),
                        Text(
                          'Chưa có cuốc xe nào',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Hãy tạo cuốc xe mới để bắt đầu',
                          style: TextStyle(
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => _realTimeService.refreshRides(),
                  child: ListView.builder(
                    padding: EdgeInsets.all(16),
                    itemCount: availableRides.length,
                    itemBuilder: (context, index) {
                      final ride = availableRides[index];
                      return _buildRideCard(ride);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, '/driver/create-ride'),
        backgroundColor: Colors.blue,
        child: Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildRideCard(dynamic ride) {
    final isHighlighted = ride.priority == 'high';
    final cardColor = isHighlighted ? Colors.yellow.shade50 : Colors.white;
    final borderColor = isHighlighted ? Colors.orange : Colors.grey.shade300;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Cùng tuyến',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${ride.totalSeats ?? 7} seats',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 12,
                    ),
                  ),
                ),
                Spacer(),
                if (isHighlighted)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Cùng huyện',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: 12),
            
            Text(
              '${NumberFormat('#,###').format(ride.totalPrice ?? 250000)} VNĐ',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            SizedBox(height: 8),
            
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                SizedBox(width: 4),
                Text(
                  ride.departureTime != null 
                    ? DateFormat('HH:mm - Hôm nay').format(ride.departureTime!)
                    : '15:03 - Hôm nay',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
              ],
            ),
            SizedBox(height: 4),
            
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '${ride.pickupLocation ?? "Đại Kinh tế"} → ${ride.destination ?? "Biên Hòa"}',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            SizedBox(height: 4),
            
            Row(
              children: [
                Icon(Icons.phone, size: 16, color: Colors.grey.shade600),
                SizedBox(width: 4),
                Text(
                  'Zalo: ${ride.contactPhone ?? "khach015"}',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
              ],
            ),
            SizedBox(height: 12),
            
            Row(
              children: [
                Text(
                  'Hoa hồng: ${NumberFormat('#,###').format((ride.totalPrice ?? 250000) * 0.05)} VNĐ (5%)',
                  style: TextStyle(
                    color: Colors.green.shade600,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Spacer(),
                ElevatedButton(
                  onPressed: () => _acceptRide(ride),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade600,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.check, color: Colors.white, size: 16),
                      SizedBox(width: 4),
                      Text(
                        'Nhận Cuốc',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _acceptRide(dynamic ride) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Xác nhận nhận cuốc'),
        content: Text('Bạn có chắc chắn muốn nhận cuốc xe này?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Đã nhận cuốc xe thành công!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('Xác nhận'),
          ),
        ],
      ),
    );
  }
}
