import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../core/app_config.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/app_theme.dart';
import '../../auth/providers/auth_provider.dart';
import '../../booking/providers/booking_provider.dart';
import '../../../services/real_time_service.dart';
import '../../../services/mock_data_service.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const AdminOverviewPage(),
    const AdminBookingsPage(),
    const AdminDriversPage(),
    const AdminSettingsPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quản trị hệ thống'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<BookingProvider>().refreshBookings();
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'profile') {
                AppRoutes.pushNamed(context, AppRoutes.profile);
              } else if (value == 'logout') {
                _logout();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person),
                    SizedBox(width: 8),
                    Text('Thông tin cá nhân'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout),
                    SizedBox(width: 8),
                    Text('Đăng xuất'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Tổng quan',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.local_taxi),
            label: 'Cuốc xe',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Tài xế',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Cài đặt',
          ),
        ],
      ),
    );
  }

  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận'),
        content: const Text('Bạn có chắc chắn muốn đăng xuất?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Đăng xuất'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      await context.read<AuthProvider>().logout();
      AppRoutes.pushNamedAndRemoveUntil(context, AppRoutes.login);
    }
  }
}

class AdminOverviewPage extends StatelessWidget {
  const AdminOverviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: AppTheme.primaryColor,
                        child: const Icon(
                          Icons.admin_panel_settings,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                return Text(
                                  'Xin chào, ${authProvider.currentUser?.fullName ?? 'Admin'}!',
                                  style: AppTheme.headingMedium,
                                );
                              },
                            ),
                            const Text(
                              'Chào mừng bạn đến với trang quản trị',
                              style: AppTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Statistics Cards
          const Text(
            'Thống kê tổng quan',
            style: AppTheme.headingMedium,
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'Tổng cuốc xe',
                  value: '156',
                  icon: Icons.local_taxi,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  title: 'Hoàn thành',
                  value: '142',
                  icon: Icons.check_circle,
                  color: AppTheme.successColor,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'Đang chờ',
                  value: '8',
                  icon: Icons.pending,
                  color: AppTheme.warningColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  title: 'Đã hủy',
                  value: '6',
                  icon: Icons.cancel,
                  color: AppTheme.errorColor,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Quick Actions
          const Text(
            'Thao tác nhanh',
            style: AppTheme.headingMedium,
          ),
          const SizedBox(height: 12),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _buildActionCard(
                title: 'Quản lý cuốc xe',
                icon: Icons.local_taxi,
                onTap: () {
                  // Switch to bookings tab
                },
              ),
              _buildActionCard(
                title: 'Quản lý tài xế',
                icon: Icons.people,
                onTap: () {
                  // Switch to drivers tab
                },
              ),
              _buildActionCard(
                title: 'Báo cáo',
                icon: Icons.analytics,
                onTap: () {
                  // Navigate to reports
                },
              ),
              _buildActionCard(
                title: 'Cài đặt',
                icon: Icons.settings,
                onTap: () {
                  // Switch to settings tab
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: AppTheme.headingLarge.copyWith(color: color),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: AppTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Placeholder pages for other tabs
class AdminBookingsPage extends StatefulWidget {
  const AdminBookingsPage({super.key});

  @override
  State<AdminBookingsPage> createState() => _AdminBookingsPageState();
}

class _AdminBookingsPageState extends State<AdminBookingsPage> {
  final RealTimeService _realTimeService = RealTimeService();
  List<dynamic> _rides = [];
  Map<String, dynamic> _stats = {};
  bool _isLoading = true;
  String _selectedDate = DateFormat('MM/dd/yyyy').format(DateTime.now());

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    _realTimeService.startRidesAutoUpdate();
    _realTimeService.startStatsAutoUpdate();
  }

  @override
  void dispose() {
    _realTimeService.stopRidesAutoUpdate();
    _realTimeService.stopStatsAutoUpdate();
    super.dispose();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    // Simulate API call
    await Future.delayed(Duration(seconds: 1));

    final mockRides = MockDataService.generateMockRides(count: 30);
    final mockStats = MockDataService.generateDashboardStats();

    setState(() {
      _rides = mockRides.map((ride) => {
        'id': ride.id,
        'driverName': ride.driverName,
        'pickupLocation': ride.fromLocation,
        'destination': ride.toLocation,
        'departureTime': ride.departureTime,
        'totalPrice': ride.pricePerSeat,
        'commission': ride.commission,
        'status': _getRandomStatus(),
        'totalSeats': ride.totalSeats,
        'driverPhone': ride.driverPhone,
      }).toList();
      _stats = mockStats;
      _isLoading = false;
    });
  }

  String _getRandomStatus() {
    final statuses = ['completed', 'in_progress', 'cancelled', 'pending'];
    return statuses[DateTime.now().millisecond % statuses.length];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.directions_car, color: Colors.blue),
            SizedBox(width: 8),
            Text(
              'Hệ Thống Đặt Xe',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          Container(
            margin: EdgeInsets.only(right: 8),
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Text(
                'Tài Xế',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(right: 16),
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey.shade300,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.admin_panel_settings, color: Colors.grey.shade700, size: 16),
                  SizedBox(width: 4),
                  Text(
                    'Admin',
                    style: TextStyle(color: Colors.grey.shade700, fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Header Section
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tổng Quan Hệ Thống',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                SizedBox(height: 16),

                // Stats Cards
                StreamBuilder<Map<String, dynamic>>(
                  stream: _realTimeService.statsStream,
                  initialData: _stats,
                  builder: (context, snapshot) {
                    final stats = snapshot.data ?? _stats;
                    return Row(
                      children: [
                        Expanded(child: _buildStatCard(
                          'Cuốc xe hôm nay',
                          '${stats['totalRides'] ?? 0}',
                          Icons.directions_car,
                          Colors.blue,
                        )),
                        SizedBox(width: 12),
                        Expanded(child: _buildStatCard(
                          'Tài xế hoạt động',
                          '${stats['activeDrivers'] ?? 2}',
                          Icons.person,
                          Colors.blue,
                        )),
                        SizedBox(width: 12),
                        Expanded(child: _buildStatCard(
                          'Doanh thu hôm nay',
                          '${(stats['todayRevenue'] ?? 0.0) / 1000000}M',
                          Icons.attach_money,
                          Colors.green,
                        )),
                        SizedBox(width: 12),
                        Expanded(child: _buildStatCard(
                          'Hoa hồng thu được',
                          '${(stats['todayCommission'] ?? 0.0) / 1000000}M',
                          Icons.percent,
                          Colors.orange,
                        )),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),

          // Tab Navigation
          Container(
            color: Colors.white,
            child: Row(
              children: [
                Expanded(child: _buildTab('Quản lý cuốc xe', Icons.list_alt, true)),
                Expanded(child: _buildTab('Quản lý tài xế', Icons.people, false)),
                Expanded(child: _buildTab('Cấu hình hệ thống', Icons.settings, false)),
                Expanded(child: _buildTab('Báo cáo', Icons.analytics, false)),
              ],
            ),
          ),

          // Connection Status
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: StreamBuilder<List<dynamic>>(
              stream: _realTimeService.ridesStream,
              builder: (context, snapshot) {
                return Row(
                  children: [
                    Icon(
                      Icons.wifi,
                      color: snapshot.hasData ? Colors.green : Colors.grey,
                      size: 16,
                    ),
                    SizedBox(width: 4),
                    Text(
                      snapshot.hasData ? 'Cập nhật sau 3s' : 'Đang kết nối...',
                      style: TextStyle(
                        color: snapshot.hasData ? Colors.green : Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),

          // Content
          Expanded(
            child: Container(
              color: Colors.white,
              child: Column(
                children: [
                  // Header with date filter
                  Container(
                    padding: EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Danh sách cuốc xe',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              'Tất cả trạng thái',
                              style: TextStyle(fontSize: 14),
                            ),
                            SizedBox(width: 16),
                            GestureDetector(
                              onTap: _selectDate,
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  children: [
                                    Text(_selectedDate, style: TextStyle(fontSize: 14)),
                                    SizedBox(width: 4),
                                    Icon(Icons.calendar_today, size: 16),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Table Header
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    color: Colors.grey.shade100,
                    child: Row(
                      children: [
                        Expanded(flex: 3, child: Text('CUỐC XE', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('TÀI XẾ', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('TRẠNG THÁI', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('GIÁ', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('HOA HỒNG', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 1, child: Text('THAO TÁC', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                      ],
                    ),
                  ),

                  // Rides List
                  Expanded(
                    child: _isLoading
                        ? Center(child: CircularProgressIndicator())
                        : StreamBuilder<List<dynamic>>(
                            stream: _realTimeService.ridesStream,
                            initialData: _rides,
                            builder: (context, snapshot) {
                              final rides = snapshot.data ?? _rides;
                              return ListView.builder(
                                itemCount: rides.length,
                                itemBuilder: (context, index) {
                                  final ride = rides[index];
                                  return _buildRideRow(ride, index);
                                },
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              Spacer(),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title, IconData icon, bool isActive) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isActive ? Colors.blue : Colors.grey.shade300,
            width: isActive ? 3 : 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: isActive ? Colors.blue : Colors.grey.shade600,
            size: 16,
          ),
          SizedBox(width: 4),
          Text(
            title,
            style: TextStyle(
              color: isActive ? Colors.blue : Colors.grey.shade600,
              fontSize: 12,
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRideRow(dynamic ride, int index) {
    final status = ride['status'] as String;
    final statusColor = _getStatusColor(status);
    final statusText = _getStatusText(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${ride['pickupLocation']} → ${ride['destination']}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  DateFormat('dd/MM/yyyy HH:mm').format(ride['departureTime']),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ride['driverName'],
                  style: TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  '${ride['totalSeats']} seats',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                statusText,
                style: TextStyle(
                  color: statusColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '${NumberFormat('#,###').format(ride['totalPrice'])} VNĐ',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '${NumberFormat('#,###').format(ride['commission'])} VNĐ',
              style: TextStyle(
                fontSize: 14,
                color: Colors.green.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: PopupMenuButton<String>(
              icon: Icon(Icons.more_vert, size: 16),
              onSelected: (value) => _handleRideAction(value, ride),
              itemBuilder: (context) => [
                PopupMenuItem(value: 'view', child: Text('Chi tiết')),
                PopupMenuItem(value: 'edit', child: Text('Sửa')),
                if (status == 'pending')
                  PopupMenuItem(value: 'cancel', child: Text('Hủy')),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'in_progress':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      case 'pending':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'in_progress':
        return 'Đang thực hiện';
      case 'cancelled':
        return 'Đã hủy';
      case 'pending':
        return 'Đang chờ nhận';
      default:
        return 'Không xác định';
    }
  }

  void _handleRideAction(String action, dynamic ride) {
    switch (action) {
      case 'view':
        // Show ride details
        break;
      case 'edit':
        // Edit ride
        break;
      case 'cancel':
        // Cancel ride
        break;
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(Duration(days: 365)),
      lastDate: DateTime.now().add(Duration(days: 30)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = DateFormat('MM/dd/yyyy').format(date);
      });
    }
  }
}

class AdminDriversPage extends StatelessWidget {
  const AdminDriversPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Trang quản lý tài xế - Sẽ được phát triển'),
    );
  }
}

class AdminSettingsPage extends StatelessWidget {
  const AdminSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Trang cài đặt hệ thống - Sẽ được phát triển'),
    );
  }
}
