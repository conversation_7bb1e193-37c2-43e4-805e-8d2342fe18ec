import 'package:flutter/material.dart';
import 'features/driver/screens/available_rides_screen.dart';
import 'features/driver/screens/create_ride_screen.dart';
import 'features/driver/screens/ride_history_screen.dart';
import 'features/admin/screens/admin_dashboard_screen.dart';
import 'features/profile/screens/profile_screen.dart';
import 'features/auth/screens/login_screen.dart';
import 'features/auth/screens/register_screen.dart';

class DemoApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Car Booking Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Roboto',
      ),
      debugShowCheckedModeBanner: false,
      initialRoute: '/demo',
      routes: {
        '/demo': (context) => DemoHomeScreen(),
        '/driver/available-rides': (context) => AvailableRidesScreen(),
        '/driver/create-ride': (context) => CreateRideScreen(),
        '/driver/ride-history': (context) => RideHistoryScreen(),
        '/admin/dashboard': (context) => AdminDashboardScreen(),
        '/profile': (context) => ProfileScreen(),
        '/login': (context) => LoginScreen(),
        '/register': (context) => RegisterScreen(),
      },
    );
  }
}

class DemoHomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.directions_car, color: Colors.blue),
            SizedBox(width: 8),
            Text(
              'Car Booking Demo',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Demo Screens',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            SizedBox(height: 20),
            
            Text(
              'Driver Screens',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            SizedBox(height: 12),
            
            _buildDemoCard(
              context,
              title: 'Tạo Cuốc Xe',
              subtitle: 'Màn hình tạo cuốc xe mới với UI theo thiết kế',
              icon: Icons.add_circle,
              color: Colors.blue,
              onTap: () => Navigator.pushNamed(context, '/driver/create-ride'),
            ),
            
            _buildDemoCard(
              context,
              title: 'Cuốc Xe Khả Dụng',
              subtitle: 'Danh sách cuốc xe với real-time updates mỗi 5s',
              icon: Icons.list_alt,
              color: Colors.green,
              onTap: () => Navigator.pushNamed(context, '/driver/available-rides'),
            ),
            
            _buildDemoCard(
              context,
              title: 'Lịch Sử Cuốc Xe',
              subtitle: 'Lịch sử các cuốc xe đã thực hiện',
              icon: Icons.history,
              color: Colors.orange,
              onTap: () => Navigator.pushNamed(context, '/driver/ride-history'),
            ),
            
            SizedBox(height: 20),
            
            Text(
              'Admin Screens',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            SizedBox(height: 12),
            
            _buildDemoCard(
              context,
              title: 'Admin Dashboard',
              subtitle: 'Dashboard quản trị với thống kê real-time',
              icon: Icons.dashboard,
              color: Colors.purple,
              onTap: () => Navigator.pushNamed(context, '/admin/dashboard'),
            ),

            SizedBox(height: 20),

            Text(
              'User Screens',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            SizedBox(height: 12),

            _buildDemoCard(
              context,
              title: 'Thông Tin Cá Nhân',
              subtitle: 'Profile user với thống kê và cài đặt',
              icon: Icons.person,
              color: Colors.teal,
              onTap: () => Navigator.pushNamed(context, '/profile'),
            ),

            _buildDemoCard(
              context,
              title: 'Đăng Nhập',
              subtitle: 'Màn hình đăng nhập responsive',
              icon: Icons.login,
              color: Colors.indigo,
              onTap: () => Navigator.pushNamed(context, '/login'),
            ),

            _buildDemoCard(
              context,
              title: 'Đăng Ký',
              subtitle: 'Màn hình đăng ký responsive',
              icon: Icons.person_add,
              color: Colors.cyan,
              onTap: () => Navigator.pushNamed(context, '/register'),
            ),
            
            Spacer(),
            
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Tính năng Demo',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Real-time updates mỗi 5 giây\n'
                    '• UI responsive cho mobile\n'
                    '• Google Maps integration (mock)\n'
                    '• Mock data với dữ liệu thực tế\n'
                    '• Smooth animations và transitions',
                    style: TextStyle(
                      color: Colors.blue.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey.shade400,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Demo main function
void runDemoApp() {
  runApp(DemoApp());
}
