import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../services/mock_data_service.dart';

class RideHistoryScreen extends StatefulWidget {
  @override
  _RideHistoryScreenState createState() => _RideHistoryScreenState();
}

class _RideHistoryScreenState extends State<RideHistoryScreen> {
  List<dynamic> _rides = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadRideHistory();
  }

  Future<void> _loadRideHistory() async {
    setState(() => _isLoading = true);

    // Simulate API call
    await Future.delayed(Duration(seconds: 1));

    // Generate mock ride history
    final mockRides = _generateMockRideHistory();

    setState(() {
      _rides = mockRides;
      _isLoading = false;
    });
  }

  List<Map<String, dynamic>> _generateMockRideHistory() {
    return [
      {
        'id': '1',
        'totalPrice': 250000.0,
        'departureTime': DateTime.now().subtract(Duration(days: 1)),
        'pickupLocation': 'ĐH Kinh tế',
        'destination': '<PERSON><PERSON><PERSON><PERSON>a',
        'contactPhone': 'khach015',
        'status': 'completed',
        'commission': 12500.0,
        'driverName': 'Nguyễn Văn A',
        'totalSeats': 7,
        'vehicleType': '7 seats',
      },
      {
        'id': '2',
        'totalPrice': 350000.0,
        'departureTime': DateTime.now().subtract(Duration(days: 2)),
        'pickupLocation': 'Bến xe Miền Đông',
        'destination': 'Sân bay Tân Sơn Nhất',
        'contactPhone': 'khach001',
        'status': 'completed',
        'commission': 17500.0,
        'driverName': 'Trần Thị B',
        'totalSeats': 4,
        'vehicleType': '4 seats',
      },
      {
        'id': '3',
        'totalPrice': 380000.0,
        'departureTime': DateTime.now().subtract(Duration(days: 3)),
        'pickupLocation': 'Lotte Center',
        'destination': 'Bà Rịa',
        'contactPhone': 'khach013',
        'status': 'cancelled',
        'commission': 0.0,
        'driverName': 'Lê Văn C',
        'totalSeats': 7,
        'vehicleType': '7 seats',
      },
      {
        'id': '4',
        'totalPrice': 320000.0,
        'departureTime': DateTime.now().subtract(Duration(days: 4)),
        'pickupLocation': 'Bến Thành',
        'destination': 'Vũng Tàu',
        'contactPhone': 'khach020',
        'status': 'completed',
        'commission': 16000.0,
        'driverName': 'Phạm Thị D',
        'totalSeats': 16,
        'vehicleType': '16 seats',
      },
      {
        'id': '5',
        'totalPrice': 280000.0,
        'departureTime': DateTime.now().subtract(Duration(days: 5)),
        'pickupLocation': 'Quận 1',
        'destination': 'Đồng Nai',
        'contactPhone': 'khach025',
        'status': 'in_progress',
        'commission': 14000.0,
        'driverName': 'Hoàng Văn E',
        'totalSeats': 4,
        'vehicleType': '4 seats',
      },
    ];
  }

  String _getRandomStatus() {
    final statuses = ['completed', 'in_progress', 'cancelled', 'pending'];
    return statuses[DateTime.now().millisecond % statuses.length];
  }

  List<dynamic> get _filteredRides {
    if (_selectedFilter == 'all') return _rides;
    return _rides.where((ride) => ride['status'] == _selectedFilter).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.directions_car, color: Colors.blue),
            SizedBox(width: 8),
            Text(
              'Hệ Thống Đặt Xe',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          Container(
            margin: EdgeInsets.only(right: 16),
            child: Row(
              children: [
                Icon(Icons.wifi, color: Colors.green, size: 16),
                SizedBox(width: 4),
                Text(
                  'Cập nhật sau 2s',
                  style: TextStyle(
                    color: Colors.green,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Status Bar
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade400, Colors.blue.shade600],
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Trạng thái',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                    Text(
                      'Đang hoạt động',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Vị trí hiện tại',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                    Text(
                      'Quận 1, TP.HCM',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Tab Navigation
          Container(
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.pushNamed(context, '/driver/available-rides'),
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade300, width: 2),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.list_alt, color: Colors.grey.shade600),
                          SizedBox(width: 8),
                          Text(
                            'Cuốc Xe\nKhả Dụng',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.pushNamed(context, '/driver/create-ride'),
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.shade300, width: 2),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.add_circle, color: Colors.grey.shade600),
                          SizedBox(width: 8),
                          Text(
                            'Tạo Cuốc',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(color: Colors.blue, width: 3),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.history, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'Lịch Sử',
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Header
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Lịch Sử Cuốc Xe',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                DropdownButton<String>(
                  value: _selectedFilter,
                  onChanged: (value) => setState(() => _selectedFilter = value!),
                  items: [
                    DropdownMenuItem(value: 'all', child: Text('Tất cả trạng thái')),
                    DropdownMenuItem(value: 'completed', child: Text('Đã hoàn thành')),
                    DropdownMenuItem(value: 'in_progress', child: Text('Đang thực hiện')),
                    DropdownMenuItem(value: 'cancelled', child: Text('Đã hủy')),
                    DropdownMenuItem(value: 'pending', child: Text('Đang chờ nhận')),
                  ],
                ),
              ],
            ),
          ),
          
          // Rides List
          Expanded(
            child: _isLoading
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Đang tải lịch sử...'),
                      ],
                    ),
                  )
                : _filteredRides.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.history, color: Colors.grey, size: 64),
                            SizedBox(height: 16),
                            Text(
                              'Chưa có lịch sử cuốc xe',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadRideHistory,
                        child: ListView.builder(
                          padding: EdgeInsets.all(16),
                          itemCount: _filteredRides.length,
                          itemBuilder: (context, index) {
                            final ride = _filteredRides[index];
                            return _buildRideHistoryCard(ride);
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildRideHistoryCard(dynamic ride) {
    final status = ride['status'] as String;
    final statusColor = _getStatusColor(status);
    final statusText = _getStatusText(status);
    final daysAgo = DateTime.now().difference(ride['departureTime']).inDays;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    statusText,
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Spacer(),
                Text(
                  '$daysAgo ngày trước',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            
            Text(
              '${NumberFormat('#,###').format(ride['totalPrice'])} VNĐ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            SizedBox(height: 8),
            
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                SizedBox(width: 4),
                Text(
                  DateFormat('HH:mm').format(ride['departureTime']),
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Text(
                    '${ride['pickupLocation']} → ${ride['destination']}',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            SizedBox(height: 4),
            
            Row(
              children: [
                Icon(Icons.person, size: 16, color: Colors.grey.shade600),
                SizedBox(width: 4),
                Text(
                  'Bạn tạo cuốc • Nhận bởi: ${ride['driverName']}',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
              ],
            ),
            
            if (status == 'in_progress')
              Padding(
                padding: EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    Icon(Icons.pending, size: 16, color: Colors.orange),
                    SizedBox(width: 4),
                    Text(
                      'Đang đón khách',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            
            if (status == 'completed')
              Padding(
                padding: EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, size: 16, color: Colors.green),
                    SizedBox(width: 4),
                    Text(
                      'Hoa hồng: +${NumberFormat('#,###').format(ride['commission'])} VNĐ',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'in_progress':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      case 'pending':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'in_progress':
        return 'Đang thực hiện';
      case 'cancelled':
        return 'Đã hủy';
      case 'pending':
        return 'Đang chờ nhận';
      default:
        return 'Không xác định';
    }
  }
}
